# Splendour Cafe Bot 2 - Complete Usage Guide

## 🎯 Quick Start

### Prerequisites Checklist
- [ ] Node.js 18+ installed
- [ ] FFmpeg installed and in PATH
- [ ] Anthropic API key for Claude 4 Sonnet
- [ ] `.env` file configured with API keys
- [ ] Dependencies installed (`npm install`)

### 30-Second Setup
```bash
# 1. <PERSON><PERSON> and install
git clone <repository>
cd splendour.cafe-bot2
npm install

# 2. Configure environment
echo "ANTHROPIC_API_KEY=your_key_here" > .env
echo "DISCORD_WEBHOOK_URL=your_webhook_url" >> .env

# 3. Start the system
node app/0rss/download.js
```

## 🔄 Complete Workflow Explanation

### Phase 1: RSS Download & Content Acquisition
**Entry Point:** `app/0rss/download.js`

#### Automatic Mode (Recommended)
```bash
node app/0rss/download.js
```
- Runs every 5 minutes via cron scheduler
- Monitors nyaa.si RSS feed for new releases
- Automatically processes whitelisted anime

#### Manual Mode
```bash
node app/0rss/download.js "magnet:?xt=urn:btih:HASH"
```
- Downloads specific torrent immediately
- Bypasses RSS feed checking
- Useful for testing or one-off downloads

#### What Happens Behind the Scenes
1. **Concurrency Check**: Verifies `app/isRunning.txt` to prevent multiple instances
2. **RSS Fetching**: Downloads and parses XML from nyaa.si with 3-retry mechanism
3. **Whitelist Filtering**: Compares titles against `whitelist.txt` and `whitelist_other.txt`
4. **Format Detection**: Identifies ToonsHub vs standard releases
5. **Quality Filtering**: Skips ToonsHub H.265 releases (only downloads H.264)
6. **Torrent Download**: Uses WebTorrent with progress tracking and speed monitoring
7. **File Management**: Renames files, updates processed lists, manages state
8. **Chain Trigger**: Automatically calls `app/1clear/clear.js`

#### Configuration Files
- **`app/0rss/whitelist.txt`**: Standard anime titles (one per line)
- **`app/0rss/whitelist_other.txt`**: ToonsHub-specific titles
- **`app/0rss/lastId.txt`**: Tracks last processed RSS entry ID
- **`app/0rss/processedTitles.txt`**: History of processed anime titles
- **`app/0rss/processed_ids.txt`**: History of processed RSS entry IDs

### Phase 2: Subtitle Extraction & Cleaning
**Entry Point:** `app/1clear/clear.js`

#### Execution
```bash
node app/1clear/clear.js
```
- Automatically triggered by Phase 1
- Can be run manually if MKV files exist in downloads

#### Dual Language Extraction Process
1. **File Discovery**: Scans `app/0rss/downloads/` for .mkv files
2. **English Extraction**: Primary subtitle track extraction using FFmpeg
3. **French Extraction**: Secondary language for validation (graceful failure)
4. **Dialogue Cleaning**: Removes timing, formatting, keeps only dialogue content
5. **Actor Prediction**: Uses timing-based algorithms to predict missing speaker names
6. **File Output**: Saves cleaned files to `app/1clear/extracted/`

#### Example Transformation
**Before (Raw .ass):**
```
Dialogue: 0,0:00:50.27,0:00:52.02,main,Rimuru,0,0,0,,Oczywiście, że nie.
```

**After (Cleaned):**
```
Rimuru | Oczywiście, że nie.
```

#### Error Handling
- **Missing French**: Continues with English-only processing
- **Corrupted Files**: Skips with warning, continues with next file
- **FFmpeg Errors**: Detailed logging, graceful degradation

### Phase 3: AI-Powered Translation
**Entry Point:** `app/2translate/translate.js`

#### Advanced Translation Pipeline
```bash
node app/2translate/translate.js
```

#### Tool-Based Architecture
The translation system uses Claude 4 Sonnet with specialized tools:

1. **Scene Detection Tool**: Analyzes timing gaps, speaker changes, content transitions
2. **Translation Tool**: Core translation with context awareness
3. **Second Language Validator**: Cross-references French subtitles for quality
4. **Examples Reference Tool**: Pattern matching against examples.xml database
5. **Polish Grammar Corrector**: Specialized punctuation and grammar rules
6. **Metadata Persistence Tool**: Character and terminology tracking

#### Scene-by-Scene Processing
```
For each scene:
  1. Analyze context and speakers
  2. Capture screenshot (if needed)
  3. Translate with tools
  4. Quality assessment
  5. Apply corrections
  6. Update metadata
  7. Save progress
```

#### Quality Assurance Pipeline
- **Initial Assessment**: Evaluates naturalness and accuracy
- **Conditional Validation**: Triggers second language check if quality < threshold
- **Pattern Matching**: Compares against proven translation examples
- **Grammar Correction**: High-priority punctuation fixes (auto-applied)
- **Confidence Scoring**: Provides quality metrics for each decision

### Phase 4: Final Processing & Output
**Entry Points:** `app/3replace/1separateActors.js` → `app/3replace/2applyTranslation.js`

#### Actor Separation
```bash
node app/3replace/1separateActors.js
```
- Removes actor prefixes from translated dialogue
- Preserves clean dialogue for final application
- Saves to `app/3replace/clean/`

#### Translation Application
```bash
node app/3replace/2applyTranslation.js
```
- Matches .ass files with .txt translations
- Preserves original timing and formatting
- Maintains actor assignments
- Generates final subtitle files
- Cleans up temporary directories

## 🛠️ Advanced Configuration

### Claude 4 Translation Settings
**File:** `app/2translate/config.js`

```javascript
{
  // Core Model Settings
  model: "claude-sonnet-4-20250514",
  maxTokens: 8192,
  temperature: 0.7,
  maxRetries: 3,

  // Scene Detection
  sceneDetection: {
    minSceneLength: 2,
    maxSceneLength: 80,
    timingGapThreshold: 5000,
    speakerChangeWeight: 0.3,
    timingWeight: 0.4,
    contentWeight: 0.3
  },

  // Tool Features
  secondLanguageValidation: {
    enableValidation: true,
    validationThreshold: 0.6
  },
  
  examplesReference: {
    enableExampleLookup: true,
    maxRelevantExamples: 5
  }
}
```

### Examples Database Management
**File:** `app/2translate/examples.xml`

#### Adding New Examples
```xml
<example>
    <English_Source>Your English text here</English_Source>
    <ideal_output>Twój polski tekst tutaj</ideal_output>
</example>
```

#### Categories Supported
- **Honorifics**: -san, -chan, -kun translations
- **Emotions**: Emotional expressions and reactions
- **Actions**: Physical actions and movements
- **Dialogue**: Conversational patterns
- **Cultural**: Cultural references and adaptations

## 📊 Monitoring & Debugging

### Real-Time Monitoring
- **Colored Terminal Output**: Visual progress indicators
- **Progress Bars**: Download and processing progress
- **Discord Notifications**: Real-time status updates (if configured)

### Log Analysis
```bash
# View recent activity
tail -f app/logs/info.log

# Check for errors
tail -f app/logs/error.log

# Monitor exceptions
tail -f app/logs/exception.log
```

### State Management
- **`app/isRunning.txt`**: Process lock file
- **Metadata persistence**: Character and terminology databases
- **Translation cache**: Reusable translation patterns

## 🧪 Testing & Validation

### Production Test Suite
```bash
node app/production-test.js
```

**Test Coverage:**
- Subtitle extraction (English + French)
- Scene detection and context analysis
- Translation with all tools (first 60 lines)
- Second language validation
- Examples reference functionality
- Metadata persistence
- Comprehensive reporting

### Utility Scripts
```bash
# Karaoke timing utilities
node app/karaokeNormalize.js  # Normalize to 00:00:00 start
node app/karaokeOffset.js     # Add time offset
```

## 🚨 Troubleshooting Guide

### Common Issues & Solutions

#### "Another instance of the script is already running"
**Cause:** Previous process didn't clean up properly
**Solution:** 
```bash
rm app/isRunning.txt
```

#### "No MKV files found to process"
**Cause:** No files in downloads directory
**Solution:** 
- Check `app/0rss/downloads/` for .mkv files
- Verify download phase completed successfully
- Check whitelist configuration

#### "French subtitle extraction failed"
**Cause:** MKV file doesn't contain French track
**Solution:** This is normal - processing continues with English only

#### "Translation quality assessment failed"
**Cause:** Poor initial translation quality
**Solution:** Second language validation will automatically attempt improvement

#### "Claude API rate limit exceeded"
**Cause:** Too many requests to Anthropic API
**Solution:** 
- Wait for rate limit reset
- Reduce `maxRetries` in config
- Implement longer delays between requests

### Error Recovery Mechanisms
- **Network failures**: Automatic retry with exponential backoff
- **File corruption**: Graceful skipping with detailed logging
- **API failures**: Fallback mechanisms and quality flagging
- **Critical vs non-critical**: Different handling strategies

### Performance Optimization
- **Memory management**: Efficient metadata caching
- **Concurrent processing**: Optimized for single-threaded execution
- **File I/O**: Streaming for large files
- **API efficiency**: Batched requests where possible

## 📈 Best Practices

### Whitelist Management
- Add anime titles exactly as they appear in RSS feed
- Use separate whitelists for different sources
- Regular cleanup of completed series

### Translation Quality
- Regularly update examples.xml with new patterns
- Monitor translation logs for quality issues
- Adjust scene detection parameters for specific anime types

### System Maintenance
- Regular log rotation and cleanup
- Monitor disk space in downloads directory
- Backup metadata and configuration files
- Update dependencies and API keys as needed
